@model YseStoreAdmin.Pages.Components.Products.ProductEdit

<div>
    <div id="products_inside" class="r_con_wrap" style="height: 186px;">
        <div class="center_container_1200" style="max-width: 1690px">
            <div class="return_title">
                <a href="/Products/Index?status=0">
                    <span class="return">产品管理</span>
                    <span class="s_return">/ 添加</span>
                </a>
            </div>
            <div class="global_switch_box">
                @*     <span class="btn_global_switch btn_back btn_disabled" data-url=""
                      style="display: none;"><em></em><i></i></span>
                <span class="btn_global_switch btn_next btn_custom"
                      data-url="/manage/products/products/view?id=206&amp;keyword=&amp;cateId=0&amp;status=1&amp;tagId=0"><em></em><i></i></span> *@
            </div>
            <div class="product_menu" data-url="">
                <div class="product_menu_item">
                    <button class="btn_menu_app"><em></em><span>我的应用</span><i></i></button>
                    <div class="box_my_app" style="display: none; top: 25px; opacity: 0;">
                        <dl class="drop_down">
                            <dt class="item" data-type="wholesale"><span>批发</span></dt>
                            <dt class="item" data-type="shipping_template"><span>运费模板</span></dt>
                            <dt class="item" data-type="custom_attributes"><span>定制属性</span></dt>
                            <dt class="item" data-type="screening"><span>条件筛选</span></dt>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        <form id="edit_form" class="global_form center_container_1200" style="max-width: 1690px">
            <div class="left_container">
                <div class="left_container_side">
                    <div class="global_container" data-name="basic_info">
                        <div class="big_title">基本信息</div>
                        <div class="rows clean translation multi_lang">
                            <label>产品名称<span class="box_explain"> (必填)</span></label>
                            <div class="input">
                                <div class="lang_txt lang_txt_en" style="display:block;" data-default="1" lang="en">
                                    <span class="unit_input">
                                        <div class="number_limit_relative" style="position:relative">
                                            <input data-auto-change="SeoTitle_en" type="text" class="box_input"
                                                   name="Name_en" value="@(Model.ProductDetail?.Product.Name_en)"
                                                   size="118" autocomplete="off" maxlength="255"
                                                   notnull="" number_limit="" style="width: 856px;">
                                        </div>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                        <div class="rows clean translation multi_lang">
                            <label>产品编号</label>
                            <div class="input">
                                <div class="unit_input">
                                    <div class="number_limit_relative" style="position:relative">
                                        <input type="text" class="box_input" name="Number"
                                               value="@(Model.ProductDetail?.Product.Number)" size="118"
                                               autocomplete="off" maxlength="255" number_limit="">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                        <div class="rows clean translation multi_lang box_briefdescription">
                            <label>简短介绍</label>
                            <div class="input">
                                <div class="lang_txt lang_txt_en" style="display:block;" data-default="1" lang="en">
                                    <textarea  id="BriefDescription_en"
                                              name="BriefDescription_en" maxlength="500" cols="121"
                                              class="box_textarea">@(Model.ProductDetail?.Product.BriefDescription_en)</textarea>
                                    @* data-auto-change="SeoDescription_en" *@
                                </div>
                            </div>
                        </div>
                        <div class="rows clean translation multi_lang box_briefdescription">
                            <label>简短介绍2</label>
                            <div class="input">
                                <div class="lang_txt lang_txt_en" style="display:block;" data-default="1" lang="en">
                                    <textarea  id="BriefDescription1_en"
                                              name="BriefDescription1_en" maxlength="500" cols="121"
                                              class="box_textarea">@(Model.ProductDetail?.Product.BriefDescription1_en)</textarea>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                        <div class="rows clean">
                            <label>详细描述</label>
                            <div class="input">
                                <div class="tab_txt tab_txt_en" style="display:block;" lang="en">
                                    <div class="fl"></div>
                                    <textarea id="Description_en" name="Description_en"
                                              data-change="1">@(Model.ProductDetail?.ProductDescription?.Description_en)</textarea>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                        <div class="rows clean">
                            <label>移动端详细描述</label>
                            <div class="box_explain">开启后，移动端的详细描述将替换成以下内容</div>
                            @if (Model.ProductDetail?.ProductDescription?.UsedMobile == true)
                            {
                                <div class="switchery checked">
                                    <input type="checkbox" name="UsedMobile" value="1" checked>
                                    <div class="switchery_toggler"></div>
                                    <div class="switchery_inner">
                                        <div class="switchery_state_on"></div>
                                        <div class="switchery_state_off"></div>
                                    </div>
                                </div>
                                <div class="blank15"></div>
                                <div class="input mobile_description">
                                    <textarea id="MobileDescription"
                                              name="MobileDescription"
                                              data-change="0">@(Model.ProductDetail?.ProductDescription?.MobileDescription)</textarea>
                                </div>
                            }
                            else
                            {
                                <div class="switchery">
                                    <input type="checkbox" name="UsedMobile" value="1">
                                    <div class="switchery_toggler"></div>
                                    <div class="switchery_inner">
                                        <div class="switchery_state_on"></div>
                                        <div class="switchery_state_off"></div>
                                    </div>
                                </div>
                                <div class="blank15"></div>
                                <div class="input mobile_description hide">
                                    <textarea id="MobileDescription"
                                              name="MobileDescription"
                                              data-change="0">@(Model.ProductDetail?.ProductDescription?.MobileDescription)</textarea>
                                </div>
                            }
                        </div>
                    </div>
                    <div class="global_container" data-name="pic_info">
                        <div class="big_title">
                            产品主图&amp;视频
                            <ul class="upload_menu">
                                <li data-type="video">添加产品视频</li>
                                <li data-type="gallery">从图片库中选择</li>
                            </ul>
                            <ul class="table_menu_button global_menu_button upload_select_menu" style="display: none;">
                                <li>
                                    <div class="btn_checkbox ">
                                        <em class="button"></em><input type="checkbox"
                                                                       name="select_all" value="">
                                    </div>
                                </li>
                                <li class="open">全选</li>
                                <li><a href="javascript:;" class="batch_delete_pictrue">删除</a></li>
                            </ul>
                        </div>
                        <div class="rows clean">
                            <div class="input" style="position: relative;">
                                @* 当有图片时显示grid布局 *@
                                @if (Model.ProductDetail?.ProductImages != null && Model.ProductDetail.ProductImages.Any())
                                {
                                    <div class="multi_img upload_file_multi pro_multi_img" id="PicDetail"
                                         data-load-icon="/assets/images/global/loading_oth.gif" data-listidx="0"
                                         style="display: block;">

                                        @* 回显已存在的图片 *@
                                        @for (int i = 0; i < Model.ProductDetail.ProductImages.Count; i++)
                                        {
                                            var image = Model.ProductDetail.ProductImages[i];
                                            <dl class="img isfile" num="@i">
                                                <dt class="upload_box preview_pic">
                                                    <a href="@image.PicPath" target="_blank">
                                                        <img
                                                            src="@(image.PicPath)?x-oss-process=image/format,webp/resize,m_lfit,h_240,w_240/quality,q_90"
                                                            alt="@image.Alt"
                                                            style="max-width: 100%; max-height: 100%;">
                                                    </a>
                                                    <input type="hidden" name="PicPath[]" value="@image.PicPath"
                                                           data-value="@image.PicPath" save="1">
                                                </dt>
                                                <dd class="pic_btn">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox"><input type="checkbox"></span>
                                                    </span>
                                                    <a href="javascript:;" class="myorder">
                                                        <i class="icon_multi_myorder"></i>
                                                    </a>
                                                    <a href="javascript:;" class="video_edit"><i
                                                            class="icon_video_edit"></i></a>
                                                    <a href="javascript:;" class="alt_edit">Alt</a>
                                                    <a href="javascript:;" class="video_seo_btn">SEO</a>
                                                    <input type="hidden" name="Alt[]" value="@image.Alt">
                                                    <a href="@image.PicPath" class="zoom" target="_blank">
                                                        <i class="icon_multi_view"></i>
                                                    </a>
                                                    <a href="javascript:;" class="del" rel="del">
                                                        <i class="icon_multi_delete"></i>
                                                    </a>
                                                </dd>
                                            </dl>
                                        }

                                        @* 添加新图片的空白项 *@
                                        <dl class="img show_btn" num="@Model.ProductDetail.ProductImages.Count">
                                            <dt class="upload_box preview_pic">
                                                <input type="button"
                                                       class="btn_ok upload_btn" name="submit_button"
                                                       value="上传图片" tips="">
                                                <input type="hidden" name="PicPath[]" value="" data-value="" save="0">
                                            </dt>
                                            <dd class="pic_btn">
                                                <span class="input_checkbox_box">
                                                    <span class="input_checkbox"><input type="checkbox"></span>
                                                </span>
                                                <a href="javascript:;" class="myorder">
                                                    <i class="icon_multi_myorder"></i>
                                                </a>
                                                <a href="javascript:;" class="video_edit"><i
                                                        class="icon_video_edit"></i></a>
                                                <a href="javascript:;" class="alt_edit">Alt</a>
                                                <a href="javascript:;" class="video_seo_btn">SEO</a>
                                                <input type="hidden" name="Alt[]" value="">
                                                <a href="javascript:;" class="zoom" target="_blank">
                                                    <i class="icon_multi_view"></i>
                                                </a>
                                                <a href="javascript:;" class="del" rel="del">
                                                    <i class="icon_multi_delete"></i>
                                                </a>
                                            </dd>
                                            <dd class="upload_txt">
                                                <p>上传图片</p>
                                                <p>或拖入本地图片</p>
                                            </dd>
                                        </dl>
                                    </div>
                                }
                                else
                                {
                                    @* 当没有图片时显示简单的上传组件，但保持上传按钮可见以支持图片库选择 *@
                                    <div class="multi_img upload_file_multi pro_multi_img" id="PicDetail"
                                         data-load-icon="/assets/images/global/loading_oth.gif" data-listidx="0"
                                         style="display: none;">
                                        <dl class="img show_btn" num="0">
                                            <dt class="upload_box preview_pic">
                                                <input type="button"
                                                       class="btn_ok upload_btn" name="submit_button"
                                                       value="上传图片" tips="">
                                                <input type="hidden" name="PicPath[]" value="" data-value="" save="0">
                                            </dt>
                                            <dd class="pic_btn">
                                                <span class="input_checkbox_box">
                                                    <span class="input_checkbox"><input type="checkbox"></span>
                                                </span>
                                                <a href="javascript:;" class="myorder">
                                                    <i class="icon_multi_myorder"></i>
                                                </a>
                                                <a href="javascript:;" class="video_edit"><i
                                                        class="icon_video_edit"></i></a>
                                                <a href="javascript:;" class="alt_edit">Alt</a>
                                                <a href="javascript:;" class="video_seo_btn">SEO</a>
                                                <input type="hidden" name="Alt[]" value="">
                                                <a href="javascript:;" class="zoom" target="_blank">
                                                    <i class="icon_multi_view"></i>
                                                </a>
                                                <a href="javascript:;" class="del" rel="del">
                                                    <i class="icon_multi_delete"></i>
                                                </a>
                                            </dd>
                                            <dd class="upload_txt">
                                                <p>上传图片</p>
                                                <p>或拖入本地图片</p>
                                            </dd>
                                        </dl>
                                    </div>
                                }
                                <div class="upload_file_box">
                                    <div class="txt_box">
                                        <div class="txt_btn">上传图片</div>
                                        <div class="txt_tips">
                                            或拖入本地jpg、jpeg、png、gif、webp格式的图片上传，单张大小须在10MB以内
                                        </div>
                                        <div class="txt_drag">松开鼠标上传</div>
                                    </div>
                                    <input type="file" name="Filedata" multiple="">
                                    <script id="template-upload" type="text/x-tmpl">
                                    </script>
                                    <script id="template-download" type="text/x-tmpl">
                                    </script>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="global_container" data-name="pic_info">
                        <div class="rows clean translation multi_lang">
                            <label>产品体积(单位cm)</label>
                            <div class="input">
                                <div style="display: flex; gap: 10px;">
                                    <div class="number_limit_relative" style="position:relative; flex: 1;">
                                        <input type="text" class="box_input" name="Cubage_Length"
                                               placeholder="长"
                                               value="@(Model.ProductDetail?.Product.Cubage?.Split(',').Length > 0 ? Model.ProductDetail?.Product.Cubage?.Split(',')[0] : "")"
                                               style="width: 100%; border: 1px solid #ddd; border-radius: 4px; padding: 5px;"
                                               autocomplete="off">
                                    </div>
                                    <div class="number_limit_relative" style="position:relative; flex: 1;">
                                        <input type="text" class="box_input" name="Cubage_Width"
                                               placeholder="宽"
                                               value="@(Model.ProductDetail?.Product.Cubage?.Split(',').Length > 1 ? Model.ProductDetail?.Product.Cubage?.Split(',')[1] : "")"
                                               style="width: 100%; border: 1px solid #ddd; border-radius: 4px; padding: 5px;"
                                               autocomplete="off">
                                    </div>
                                    <div class="number_limit_relative" style="position:relative; flex: 1;">
                                        <input type="text" class="box_input" name="Cubage_Height"
                                               placeholder="高"
                                               value="@(Model.ProductDetail?.Product.Cubage?.Split(',').Length > 2 ? Model.ProductDetail?.Product.Cubage?.Split(',')[2] : "")"
                                               style="width: 100%; border: 1px solid #ddd; border-radius: 4px; padding: 5px;"
                                               autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="global_container specification_container">
                        <div class="big_title">规格模式</div>
                        <div class="rows clean">
                            <div class="input">
                                @if (Model.ProductDetail?.Product.IsCombination == 0)
                                {
                                    <span class="input_radio_box input_radio_radius_box tab_option fl checked">
                                        <span class="input_radio"><input type="radio" name="IsCombination" value="0"
                                                                         checked></span>
                                        <em></em>
                                        <p>
                                            <span class="fs14">单规格</span>
                                            <span class="fs12">只能设置一个规格</span>
                                        </p>
                                    </span>
                                }
                                else
                                {
                                    <span class="input_radio_box input_radio_radius_box tab_option fl">
                                        <span class="input_radio"><input type="radio" name="IsCombination"
                                                                         value="0"></span>
                                        <em></em>
                                        <p>
                                            <span class="fs14">单规格</span>
                                            <span class="fs12">只能设置一个规格</span>
                                        </p>
                                    </span>
                                }

                                @if (Model.ProductDetail?.Product.IsCombination == 1)
                                {
                                    <span class="input_radio_box input_radio_radius_box tab_option fl checked">
                                        <span class="input_radio"><input type="radio" name="IsCombination" value="1"
                                                                         checked></span>
                                        <em></em>
                                        <p>
                                            <span class="fs14">多规格</span>
                                            <span class="fs12">可以为产品设置多个规格</span>
                                        </p>
                                    </span>
                                }
                                else
                                {
                                    <span class="input_radio_box input_radio_radius_box tab_option fl">
                                        <span class="input_radio"><input type="radio" name="IsCombination"
                                                                         value="1"></span>
                                        <em></em>
                                        <p>
                                            <span class="fs14">多规格</span>
                                            <span class="fs12">可以为产品设置多个规格</span>
                                        </p>
                                    </span>
                                }

                                @if (Model.ProductDetail?.Product.IsCombination == 2)
                                {
                                    <span class="input_radio_box input_radio_radius_box tab_option fl checked">
                                        <span class="input_radio"><input type="radio" name="IsCombination" value="2"
                                                                         checked></span>
                                        <em></em>
                                        <p>
                                            <span class="fs14">多规格加价</span>
                                            <span class="fs12">只能给单规格产品设置组合属性</span>
                                        </p>
                                    </span>
                                }
                                else
                                {
                                    <span class="input_radio_box input_radio_radius_box tab_option fl">
                                        <span class="input_radio"><input type="radio" name="IsCombination"
                                                                         value="2"></span>
                                        <em></em>
                                        <p>
                                            <span class="fs14">多规格加价</span>
                                            <span class="fs12">只能给单规格产品设置组合属性</span>
                                        </p>
                                    </span>
                                }
                            </div>
                        </div>
                    </div>
                    
                    <div class="global_container default_inventory_container" data-name="pic_info"
                         style="display: @(Model.ProductDetail?.Product.IsCombination == 1 ? "block" : "none");">
                        <div class="rows clean translation multi_lang">
                            <label>设置默认库存</label>
                            <div class="input">
                                <div style="display: flex; gap: 10px;">
                                    <div class="number_limit_relative" style="position:relative; flex: 1;">
                                        <input type="text" class="box_input" name="DefaultInventory"
                                               placeholder="默认库存"
                                               value="5000"
                                               style="width: 100%; border: 1px solid #ddd; border-radius: 4px; padding: 5px;"
                                               autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="global_container attr_container" data-name="attr_info" data-type="0">
                        <div class="big_title" style="display: none;">规格信息</div>
                        <div class="box_attribute_tab_menu" style="display: block;">
                            <span class="item checked" data-type="info">规格信息</span>
                            <span class="item" data-type="basic" style="display: none;">基础设置</span>
                            <span class="item" data-type="price" style="display: none;">规格设置</span>
                            <span class="item" data-type="option" style="display: none;">属性管理</span>
                            <span class="item" data-type="main" style="display: none;">关联主图</span>
                        </div>
                        <div class="box_attribute" style="display: none;">
                            <div class="box_no_data attr_no_data clean" style="display: block;">
                                <div class="bg_no_table_data">
                                    <div class="content">
                                        <p>当前暂时没有数据</p><a href="javascript:;" class="btn_global btn_add_item"
                                                                  id="add_attribute_oth">添加</a>
                                    </div>
                                    <span></span>
                                </div>
                            </div>
                            <div class="box_cart_attribute" data-type="0"></div>
                            <div class="rows clean attr_add">
                                <div class="input">
                                    <a href="javascript:;" class="btn_global btn_add_item fl" id="add_attribute"
                                       data-cart="1" style="display: none;">添加属性</a>
                                    <a href="javascript:;" class="btn_global btn_add_item fl" id="myorder_attribute"
                                       style="display: none;">属性排序</a>
                                </div>
                            </div>
                            <div class="product_line"></div>
                        </div>
                        <div class="rows clean" id="attribute_ext_box" style="display: block;">
                            <div class="box_no_data spec_no_data clean" style="display: none;">
                                <div class="bg_no_table_data">
                                    <div class="content">
                                        <p>当前暂时没有数据</p>
                                    </div>
                                    <span></span>
                                </div>
                            </div>
                            <div class="box_warehouse inside_container clean" style="display: flex;">
                                <div class="inside_menu option_filter inside_menu_mini" style="display: none;">
                                    <div class="inside_title"><span>选择规格</span><i class="iconfont">&#xe772;</i>
                                    </div>
                                    <div class="inside_body">
                                        <div class="box_filter">
                                            <div class="filter_grid"></div>
                                            <a href="javascript:;"
                                               class="clear_option_filter">清除</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="inside_menu warehouse_filter inside_menu_mini">
                                    <div class="inside_title">
                                        <span>设置仓库</span><strong>设置仓库</strong><i
                                            class="iconfont">&#xe772;</i><em class="btn_warehouse_set"></em>
                                    </div>
                                    <div class="inside_body">
                                        <ul>
                                            <li data-type="default" class="hide">
                                                <a href="javascript:;"
                                                   class="btn_warehouse current" data-id="0">设置仓库</a>
                                            </li>
                                            @if (Model.StockList != null && Model.StockList.Any())
                                            {
                                                foreach (var item in Model.StockList)
                                                {
                                                    <li data-type="warehouse" class="hide">
                                                        <a href="javascript:;"
                                                           class="btn_warehouse" data-id="@item.Value">@item.Text</a>
                                                    </li>
                                                }
                                            }
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="input box_multi_tab" style="display: none;">
                                <div class="attribute_ext" id="AttrId_1" data-number="1">
                                    <div class="box_table">
                                        <table border="0" cellpadding="5" cellspacing="0" class="relation_box">
                                            <colgroup>
                                                <col style="width:70px;">
                                                <col style="width:208px;">
                                                <col style="width:60px;">
                                                <col>
                                                <col>
                                                <col>
                                                <col>
                                                <col>
                                                <col>
                                                <col style="width:120px;">
                                            </colgroup>
                                            <thead>
                                            <tr>
                                                <td class="flex_item attr_checkbox" style="left: 0px;">
                                                    <ul class="table_menu_button global_menu_button">
                                                        <li>
                                                            <div class="btn_checkbox ">
                                                                <em class="button"></em><input type="checkbox"
                                                                                               name="select_all"
                                                                                               value="">
                                                            </div>
                                                        </li>
                                                        <li class="open no_select">已选择<span></span>个</li>
                                                        <li><a href="javascript:;" class="batch_delete">删除</a></li>
                                                        <li><a href="javascript:;" class="batch_edit">批量处理</a></li>
                                                        <li>
                                                            <a href="javascript:;"
                                                               class="set_default">设置为默认选项</a>
                                                        </li>
                                                    </ul>
                                                </td>
                                                <td nowrap="" class="flex_item" style="left: 62px;">规格</td>
                                                <td>配图</td>
                                                <td>SKU</td>
                                                <td>原价(@Model.Currency)</td>
                                                <td>价格(@Model.Currency)</td>
                                                <td>成本价(@Model.Currency)</td>
                                                <td>库存</td>
                                                <td>重量</td>
                                                <td>优先发货仓库</td>
                                            </tr>
                                            </thead>
                                            <tbody data-id="0"></tbody>
                                            <tbody data-id="1" style="display: none;"></tbody>
                                            <tbody data-id="2" style="display: none;"></tbody>
                                        </table>
                                    </div>
                                    <div class="scroll_sticky">
                                        <div class="scroll_sticky_content">
                                            <div style="width: 100px; height: 1px;"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="attribute_ext" id="AttrId_0" data-number="0">
                                    <table border="0" cellpadding="5" cellspacing="0" class="relation_box">
                                        <colgroup>
                                            <col style="width:70px;">
                                            <col style="width:208px;">
                                            <col style="width:190px;">
                                            <col style="width:190px;">
                                        </colgroup>
                                        <thead>
                                        <tr>
                                            <td class="attr_checkbox">
                                                <ul class="table_menu_button global_menu_button">
                                                    <li>
                                                        <div class="btn_checkbox ">
                                                            <em class="button"></em><input type="checkbox"
                                                                                           name="select_all" value="">
                                                        </div>
                                                    </li>
                                                    <li class="open no_select">已选择<span></span>个</li>
                                                    <li><a href="javascript:;" class="batch_edit">批量处理</a></li>
                                                </ul>
                                            </td>
                                            <td>属性</td>
                                            <td>原价加价(@Model.Currency)</td>
                                            <td>价格加价(@Model.Currency)</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                                <div id="attribute_tmp" class="hide">
                                    <table class="column">
                                        <tbody></tbody>
                                    </table>
                                    <table>
                                        <tbody class="contents" id="tmp_1">
                                        <tr attr_txt="">
                                            <td class="flex_item attr_checkbox">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox">
                                                        </span>
                                                    </span>
                                                <input type="hidden" name="variants[%OvId%][%Position%][VariantsId]"
                                                       value="%VID%">
                                                <input type="hidden" name="variants[%OvId%][%Position%][Attr]"
                                                       value="%Attr%">
                                                <input type="hidden" name="variants[%OvId%][%Position%][Options]"
                                                       value="%Options%">
                                                <input type="hidden" name="variants[%OvId%][%Position%][Position]"
                                                       value="%Position%">
                                                <input type="hidden" name="variants[%OvId%][%Position%][IsDefault]"
                                                       value="%IsDefault%">
                                            </td>
                                            <td class="flex_item attr_name">Name</td>
                                            <td>
                                                <div data-pic=""></div>
                                                <input type="hidden"
                                                       name="variants[%OvId%][%Position%][PicPath]" value="%IMG%">
                                            </td>
                                            <td>
                                                <input type="text" class="box_input"
                                                       name="variants[%OvId%][%Position%][SKU]" value="%SKUV%"
                                                       size="11" maxlength="255" data-type="sku" autocomplete="off">
                                            </td>
                                            <td>
                                                <input type="text" class="box_input"
                                                       name="variants[%OvId%][%Position%][OldPrice]" value="%OV%"
                                                       size="6" maxlength="10" rel="amount" placeholder="0.00"
                                                       data-type="oldprice" autocomplete="off">
                                            </td>
                                            <td>
                                                <input type="text" class="box_input"
                                                       name="variants[%OvId%][%Position%][Price]" value="%PV%" size="6"
                                                       maxlength="10" rel="amount" placeholder="0.00" data-type="price"
                                                       autocomplete="off">
                                            </td>
                                            <td>
                                                <input type="text" class="box_input"
                                                       name="variants[%OvId%][%Position%][CostPrice]" value="%CV%"
                                                       size="6" maxlength="10" rel="amount" placeholder="0.00"
                                                       data-type="cost_price" autocomplete="off">
                                            </td>
                                            <td>
                                                <input type="text" class="box_input"
                                                       name="variants[%OvId%][%Position%][Stock]" value="%SV%" size="4"
                                                       maxlength="10" rel="int" placeholder="0" data-type="stock"
                                                       data-key="-" autocomplete="off">
                                            </td>
                                            <td>
                                                    <span class="unit_input">
                                                        <input type="text" class="box_input"
                                                               name="variants[%OvId%][%Position%][Weight]" value="%WV%"
                                                               size="5" maxlength="10" rel="amount" placeholder="0.00"
                                                               data-type="weight" autocomplete="off">
                                                        <b class="last box_select_down">
                                                            <span class="head">%WUV%</span>
                                                            <ul class="list">
                                                                <li data-value="kg">kg</li>
                                                                <li data-value="g">g</li>
                                                                <li data-value="lb">lb</li>
                                                                <li data-value="oz">oz</li>
                                                            </ul>
                                                            <input type="hidden" class="unit_input_select_input"
                                                                   name="variants[%OvId%][%Position%][WeightUnit]"
                                                                   value="%WUV%">
                                                        </b>
                                                    </span>
                                            </td>
                                            <td>
                                                <select name="variants[%OvId%][%Position%][PriorityShippingOvId]" data-type="priority_shipping" style="width: 100%; padding: 4px; border: 1px solid #ddd; border-radius: 3px; background-color: white;">
                                                    %PRIORITY_SHIPPING_OPTIONS%
                                                </select>
                                            </td>
                                        </tr>
                                        </tbody>
                                        <tbody class="contents" id="tmp_0">
                                        <tr attr_txt="">
                                            <td class="attr_checkbox">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox">
                                                        </span>
                                                    </span>
                                                <input type="hidden" name="variants[0][%Position%][VariantsId]"
                                                       value="%VID%">
                                                <input type="hidden" name="variants[0][%Position%][Attr]"
                                                       value="%Attr%">
                                                <input type="hidden" name="variants[0][%Position%][Options]"
                                                       value="%Options%">
                                                <input type="hidden" name="variants[0][%Position%][Position]"
                                                       value="%Position%">
                                            </td>
                                            <td class="attr_name">Name</td>
                                            <td>
                                                <input type="text" class="box_input"
                                                       name="variants[0][%Position%][OldPrice]" value="%OV%" size="6"
                                                       maxlength="10" rel="amount" placeholder="0.00"
                                                       data-type="oldprice" autocomplete="off">
                                            </td>
                                            <td>
                                                <input type="text" class="box_input"
                                                       name="variants[0][%Position%][Price]" value="%PV%" size="6"
                                                       maxlength="10" rel="amount" placeholder="0.00" data-type="price"
                                                       autocomplete="off">
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <div id="icon_picture_html">
                                        <div class="multi_img upload_file_multi " id="XXXDetail">
                                            <dl class="img " num="0">
                                                <dt class="upload_box preview_pic">
                                                    <input type="button"
                                                           class="btn_ok upload_btn" name="submit_button"
                                                           value="上传图片"
                                                           tips=""><input type="hidden" name="XXXPath" value=""
                                                                          data-value="" save="0">
                                                </dt>
                                                <dd class="pic_btn">
                                                    <a href="javascript:;" class="zoom"
                                                       target="_blank"><i class="icon_multi_view"></i></a><a
                                                        href="javascript:;" class="del" rel="del">
                                                        <i class="icon_multi_delete"></i>
                                                    </a>
                                                </dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="attribute_unit_box">
                            <table border="0" cellpadding="5" cellspacing="0" class="relation_box">
                                <colgroup>
                                    <col style="width:16.66%;">
                                    <col style="width:16.66%;">
                                    <col style="width:16.66%;">
                                    <col style="width:16.66%;">
                                    <col style="width:16.66%;">
                                    <col style="width:16.66%;">
                                </colgroup>
                                <thead>
                                <tr>
                                    <td class="color_000">原价(@Model.Currency)</td>
                                    <td class="color_000">价格(@Model.Currency)</td>
                                    <td class="color_000">成本价</td>
                                    <td class="color_000">SKU</td>
                                    <td class="color_000">重量</td>
                                    <td class="color_000">库存</td>
                                </tr>
                                </thead>
                                <tbody class="contents">
                                @if (Model.ProductDetail?.BasicSpecificationInformation != null && Model.ProductDetail.BasicSpecificationInformation.Any())
                                {
                                    @foreach (var spec in Model.ProductDetail.BasicSpecificationInformation)
                                    {
                                        if (spec.WarehouseId == 0)
                                        {
                                            <tr data-id="0">
                                                <td>
                                                    <input type="text" class="box_input" name="Price_0"
                                                           value="@(spec.OldPrice.ToString("F2"))"
                                                           size="16"
                                                           maxlength="10" rel="amount" placeholder="0.00"
                                                           autocomplete="off">
                                                </td>
                                                <td>
                                                    <input type="text" class="box_input" name="Price_1"
                                                           value="@(spec.Price.ToString("F2"))"
                                                           size="16"
                                                           maxlength="10" rel="amount" placeholder="0.00"
                                                           autocomplete="off">
                                                </td>
                                                <td>
                                                    <input type="text" class="box_input" name="CostPrice"
                                                           value="@(spec.CostPrice.ToString("F2"))" size="16"
                                                           maxlength="10" rel="amount" placeholder="0.00"
                                                           autocomplete="off">
                                                </td>
                                                <td>
                                                    <input type="text" class="box_input" name="SKU"
                                                           value="@(spec.SKU)" maxlength="50"
                                                           autocomplete="off">
                                                </td>
                                                <td>
                                                    <span class="unit_input">
                                                        <input type="text" class="box_input" name="Weight"
                                                               value="@(spec.Weight)"
                                                               maxlength="11" rel="amount" autocomplete="off">
                                                        <b class="last box_select_down">
                                                            <span class="head">@(spec.WeightUnit)</span>
                                                            <ul class="list">
                                                                <li data-value="kg"
                                                                    class="@(spec.WeightUnit == "kg" ? "current" : "")">kg</li>
                                                                <li data-value="g"
                                                                    class="@(spec.WeightUnit == "g" ? "current" : "")">g</li>
                                                                <li data-value="lb"
                                                                    class="@(spec.WeightUnit == "lb" ? "current" : "")">lb</li>
                                                                <li data-value="oz"
                                                                    class="@(spec.WeightUnit == "oz" ? "current" : "")">oz</li>
                                                            </ul>
                                                            <input type="hidden" class="unit_input_select_input"
                                                                   name="WeightUnit"
                                                                   value="@(spec.WeightUnit)">
                                                        </b>
                                                    </span>
                                                </td>
                                                <td>
                                                    <input type="text" class="box_input" name="Stock"
                                                           value="@(spec.Stock)" maxlength="255"
                                                           notnull="notnull" rel="int" data-key="-" autocomplete="off">
                                                </td>
                                            </tr>
                                        }
                                        else
                                        {
                                            <tr data-type="warehouse" data-id="@spec.WarehouseId"
                                                data-disabled="@(spec.IsEnabled ? "0" : "1")"
                                                style="@(spec.IsEnabled ? "" : "display: none;")">
                                                <td>
                                                    <input type="text" class="box_input"
                                                           name="ProInfo[_@(spec.WarehouseId)][Price_0]"
                                                           value="@(spec.OldPrice.ToString("F2"))"
                                                           size="16" maxlength="10" rel="amount" placeholder="0.00"
                                                           autocomplete="off" @(spec.IsEnabled ? "" : "disabled")>
                                                </td>
                                                <td>
                                                    <input type="text" class="box_input"
                                                           name="ProInfo[_@(spec.WarehouseId)][Price_1]"
                                                           value="@(spec.Price.ToString("F2"))"
                                                           size="16" maxlength="10" rel="amount" placeholder="0.00"
                                                           autocomplete="off" @(spec.IsEnabled ? "" : "disabled")>
                                                </td>
                                                <td>
                                                    <input type="text" class="box_input"
                                                           name="ProInfo[_@(spec.WarehouseId)][CostPrice]"
                                                           value="@(spec.CostPrice.ToString("F2"))"
                                                           size="16" maxlength="10" rel="amount" placeholder="0.00"
                                                           autocomplete="off" @(spec.IsEnabled ? "" : "disabled")>
                                                </td>
                                                <td>
                                                    <input type="text" class="box_input"
                                                           name="ProInfo[_@(spec.WarehouseId)][SKU]" value="@(spec.SKU)"
                                                           maxlength="50" autocomplete="off"
                                                           @(spec.IsEnabled ? "" : "disabled")>
                                                </td>
                                                <td>
                                                    <span class="unit_input">
                                                        <input type="text" class="box_input"
                                                               name="ProInfo[_@(spec.WarehouseId)][Weight]"
                                                               value="@(spec.Weight)" maxlength="11" rel="amount"
                                                               autocomplete="off"
                                                               @(spec.IsEnabled ? "" : "disabled")>
                                                        <b class="last box_select_down">
                                                            <span class="head">@(spec.WeightUnit)</span>
                                                            <ul class="list">
                                                                <li data-value="kg"
                                                                    class="@(spec.WeightUnit == "kg" ? "current" : "")">kg</li>
                                                                <li data-value="g"
                                                                    class="@(spec.WeightUnit == "g" ? "current" : "")">g</li>
                                                                <li data-value="lb"
                                                                    class="@(spec.WeightUnit == "lb" ? "current" : "")">lb</li>
                                                                <li data-value="oz"
                                                                    class="@(spec.WeightUnit == "oz" ? "current" : "")">oz</li>
                                                            </ul>
                                                            <input type="hidden" class="unit_input_select_input"
                                                                   name="ProInfo[_@(spec.WarehouseId)][WeightUnit]"
                                                                   value="@(spec.WeightUnit)"
                                                                   @(spec.IsEnabled ? "" : "disabled")>
                                                        </b>
                                                    </span>
                                                </td>
                                                <td>
                                                    <input type="text" class="box_input"
                                                           name="ProInfo[_@(spec.WarehouseId)][Stock]"
                                                           value="@(spec.Stock)"
                                                           maxlength="255" notnull="" rel="int" data-key="-"
                                                           autocomplete="off"
                                                           @(spec.IsEnabled ? "" : "disabled")>
                                                </td>
                                            </tr>
                                        }
                                    }
                                }
                                else
                                {
                                    <tr data-id="0">
                                        <td>
                                            <input type="text" class="box_input" name="Price_0"
                                                   value="@(Model.ProductDetail?.Product.Price_0?.ToString("F2"))"
                                                   size="16"
                                                   maxlength="10" rel="amount" placeholder="0.00" autocomplete="off">
                                        </td>
                                        <td>
                                            <input type="text" class="box_input" name="Price_1"
                                                   value="@(Model.ProductDetail?.Product.Price_1?.ToString("F2"))"
                                                   size="16"
                                                   maxlength="10" rel="amount" placeholder="0.00" autocomplete="off">
                                        </td>
                                        <td>
                                            <input type="text" class="box_input" name="CostPrice"
                                                   value="@(Model.ProductDetail?.Product.CostPrice)" size="16"
                                                   maxlength="10" rel="amount" placeholder="0.00" autocomplete="off">
                                        </td>
                                        <td>
                                            <input type="text" class="box_input" name="SKU"
                                                   value="@(Model.ProductDetail?.Product.SKU)" maxlength="50"
                                                   autocomplete="off">
                                        </td>
                                        <td>
                                                <span class="unit_input">
                                                    <input type="text" class="box_input" name="Weight"
                                                           value="@(Model.ProductDetail?.Product.Weight ?? 0)"
                                                           maxlength="11" rel="amount" autocomplete="off">
                                                    <b class="last box_select_down">
                                                        <span
                                                            class="head">@(Model.ProductDetail?.Product?.WeightUnit ?? "kg")</span>
                                                        <ul class="list">
                                                            <li data-value="kg"
                                                                class="@(Model.ProductDetail?.Product?.WeightUnit == "kg" || Model.ProductDetail?.Product?.WeightUnit == null ? "current" : "")">kg</li>
                                                            <li data-value="g"
                                                                class="@(Model.ProductDetail?.Product?.WeightUnit == "g" ? "current" : "")">g</li>
                                                            <li data-value="lb"
                                                                class="@(Model.ProductDetail?.Product?.WeightUnit == "lb" ? "current" : "")">lb</li>
                                                            <li data-value="oz"
                                                                class="@(Model.ProductDetail?.Product?.WeightUnit == "oz" ? "current" : "")">oz</li>
                                                        </ul>
                                                        <input type="hidden" class="unit_input_select_input"
                                                               name="WeightUnit"
                                                               value="@(Model.ProductDetail?.Product?.WeightUnit ?? "kg")">
                                                    </b>
                                                </span>
                                        </td>
                                        <td>
                                            <input type="text" class="box_input" name="Stock"
                                                   value="@(Model.ProductDetail?.Product.Stock ?? 0)" maxlength="255"
                                                   notnull="notnull" rel="int" data-key="-" autocomplete="off">
                                        </td>
                                    </tr>
                                }
                                </tbody>
                            </table>
                            <div class="clear"></div>
                        </div>
                        <div class="rows clean" id="box_main_image" style="display: none;">
                            <div class="main_image_container">
                                <div class="rows clean box_relate_method">
                                    <label>关联方式</label>
                                    <div class="input">
                                        <div class="box_type_menu">
                                            <span
                                                class="item @(Model.ProductDetail?.RelateMethod == "multiple-single" || Model.ProductDetail?.RelateMethod == null ? "checked" : "")">
                                                <input type="radio" name="RelateMethod" value="multiple-single"
                                                       @(Model.ProductDetail?.RelateMethod == "multiple-single" || Model.ProductDetail?.RelateMethod == null ? "checked" : "")>
                                                多个属性关联单张主图
                                            </span>
                                            <span
                                                class="item @(Model.ProductDetail?.RelateMethod == "single-single" ? "checked" : "")">
                                                <input type="radio" name="RelateMethod" value="single-single"
                                                       @(Model.ProductDetail?.RelateMethod == "single-single" ? "checked" : "")>
                                                单个属性关联单张主图
                                            </span>
                                            <span
                                                class="item @(Model.ProductDetail?.RelateMethod == "single-multiple" ? "checked" : "")">
                                                <input type="radio" name="RelateMethod" value="single-multiple"
                                                       @(Model.ProductDetail?.RelateMethod == "single-multiple" ? "checked" : "")>
                                                单个属性关联多张主图
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="rows clean box_main_attr" style="display: none;">
                                    <label>设置主属性</label>
                                    <div class="input">
                                        <div class="box_select">
                                            <select name="MainAttr">
                                                <option value="" data-id="0">请选择</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="rows clean box_main_associate" style="display: none;">
                                    <label>关联主图</label>
                                    <table border="0" cellpadding="5" cellspacing="0" class="relation_box">
                                        <colgroup>
                                            <col style="width:190px;">
                                            <col>
                                        </colgroup>
                                        <thead>
                                        <tr>
                                            <td>选项</td>
                                            <td>主图</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="right_container">
                <div class="global_container" data-name="stock_info">
                    <div class="big_title">库存信息</div>
                    <div class="rows clean">
                        <label>状态</label>
                        <div class="input">
                            <div class="box_select sold_status fl">
                                <select name="SoldOut">
                                    @if (Model.ProductDetail?.Product.SoldOut != true)
                                    {
                                        <option value="0" selected>上架</option>
                                    }
                                    else
                                    {
                                        <option value="0">上架</option>
                                    }

                                    @if (Model.ProductDetail?.Product.SoldOut == true)
                                    {
                                        <option value="1" selected>下架</option>
                                    }
                                    else
                                    {
                                        <option value="1">下架</option>
                                    }
                                </select>
                            </div>
                            <div id="arrival_notice_div" class="sold_status_box fl hide" style="display:none;">
                                <div class="switchery">
                                    <input type="checkbox" name="StockOut" value="1">
                                    <div class="switchery_toggler"></div>
                                    <div class="switchery_inner">
                                        <div class="switchery_state_on"></div>
                                        <div class="switchery_state_off"></div>
                                    </div>
                                </div>
                                <span class="sold_in_title">到货通知</span>
                            </div>
                        </div>
                    </div>
                    <div class="rows clean">
                        <label>跟踪库存</label>
                        <div class="input">
                            <div class="box_select sold_status fl">
                                <select name="SoldStatus">
                                    @if (Model.ProductDetail?.Product.SoldStatus == 0)
                                    {
                                        <option value="0" selected>库存为0 不允许购买</option>
                                    }
                                    else
                                    {
                                        <option value="0">库存为0 不允许购买</option>
                                    }

                                    @if (Model.ProductDetail?.Product.SoldStatus == 1)
                                    {
                                        <option value="1" selected>库存为0 允许购买</option>
                                    }
                                    else
                                    {
                                        <option value="1">库存为0 允许购买</option>
                                    }

                                    @if (Model.ProductDetail?.Product.SoldStatus == 2)
                                    {
                                        <option value="2" selected>库存为0 自动下架，库存不为0 自动上架</option>
                                    }
                                    else
                                    {
                                        <option value="2">库存为0 自动下架，库存不为0 自动上架</option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="global_container" data-name="classify_info">
                    <div class="classify_rows">
                        <div class="big_title">
                            所属分类
                        </div>
                        <div class="global_select_category_btn_box">
                            <div class="title_box">
                                <span>指定分类</span>
                                <a href="javascript:;" data-type="productsEdit"
                                   data-input-name="products_categoryCurrent"
                                   class="global_select_category_btn">选择</a>
                            </div>
                            <div class="global_select_category_value_box">
                                <div
                                    class="category_value_box @(Model.ProductCategorySelected != null && Model.ProductCategorySelected.Any() ? "" : "hide")">
                                    @if (Model.ProductCategorySelected != null && Model.ProductCategorySelected.Any())
                                    {
                                        foreach (var item in Model.ProductCategorySelected)
                                        {
                                            <div class="category_item" data-cateid="@item.Value">
                                                <div class="cname">@item.Text</div>
                                                <div class="cdel"><span class="icon iconfont icon-close1"></span></div>
                                                <input type="hidden" name="products_categoryCurrent[]"
                                                       value="@item.Value">
                                            </div>
                                        }
                                    }
                                </div>
                                <div
                                    class="no-data @(Model.ProductCategorySelected != null && Model.ProductCategorySelected.Any() ? "hide" : "")">
                                    当前暂时没有数据
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="global_container">
                    <div class="big_title">
                        需要物流
                        @if (Model.ProductDetail?.Product.isVirtual == true)
                        {
                            <div class="switchery virtual_button">
                                <input type="checkbox" value="1" name="needLogistics" checked="">
                                <div class="switchery_toggler"></div>
                                <div class="switchery_inner">
                                    <div class="switchery_state_on"></div>
                                    <div class="switchery_state_off"></div>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="switchery virtual_button  checked">
                                <input type="checkbox" value="1" name="needLogistics" checked>
                                <div class="switchery_toggler"></div>
                                <div class="switchery_inner">
                                    <div class="switchery_state_on"></div>
                                    <div class="switchery_state_off"></div>
                                </div>
                            </div>
                        }

                    </div>
                    <div class="rows clean logistics_box"
                         style="display:@(Model.ProductDetail?.Product.isVirtual == false ? "block" : "none");">
                        <label>免运费</label>
                        <div class="input">
                            @if (Model.ProductDetail?.Product.IsFreeShipping == true)
                            {
                                <div class="switchery checked">
                                    <input type="checkbox" value="1" name="IsFreeShipping" checked>
                                    <div class="switchery_toggler"></div>
                                    <div class="switchery_inner">
                                        <div class="switchery_state_on"></div>
                                        <div class="switchery_state_off"></div>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="switchery">
                                    <input type="checkbox" value="1" name="IsFreeShipping">
                                    <div class="switchery_toggler"></div>
                                    <div class="switchery_inner">
                                        <div class="switchery_state_on"></div>
                                        <div class="switchery_state_off"></div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
                <div class="global_container" style="display:none;">
                    <div class="big_title">
                        发货
                    </div>
                    <div class="customs_box" data-name="customs">
                        <div class="freight_box" data-name="freight_info" style="display:block;">
                            <div class="rows clean">
                                <label>选择运费模板</label>
                                <div class="input">
                                    <div class="box_select">
                                        <select name="TId">
                                            <option value="">请选择</option>
                                            <option value="1">Free Shipping (10-20 days)</option>
                                            <option value="3">Expedited Shipping (5-8 days)</option>
                                            <option value="4">Battery Shipping (10-20 days)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="rows clean">
                                <div class="input">
                                    <div class="box_volume_weight" style="display:inline-block;">
                                        <span class="input_checkbox_box">
                                            <span class="input_checkbox">
                                                <input type="checkbox" name="IsVolumeWeight" value="1">
                                            </span>使用体积计算运费
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="rows clean cubage_box" style="display:none;">
                                <label>体积</label>
                                <div class="input">
                                    <span class="unit_input cubage_input">
                                        <b>长</b><input type="text" class="box_input"
                                                        name="Cubage[0]" value="0" size="3" maxlength="10" rel="amount"><b
                                            class="last">m</b>
                                    </span>
                                    <span class="unit_input cubage_input">
                                        <b>宽</b><input type="text" class="box_input"
                                                        name="Cubage[1]" value="0" size="3" maxlength="10" rel="amount"><b
                                            class="last">m</b>
                                    </span>
                                    <span class="unit_input cubage_input">
                                        <b>高</b><input type="text" class="box_input"
                                                        name="Cubage[2]" value="0" size="3" maxlength="10" rel="amount"><b
                                            class="last">m</b>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>产品类型</label>
                            <div class="input">
                                <span class="input_radio_box checked">
                                    <span class="input_radio">
                                        <input type="radio" name="DustomsGoodsType" value="1" checked="checked">
                                    </span>普通货物
                                </span>&nbsp;&nbsp;&nbsp;
                                <span class="input_radio_box ">
                                    <span class="input_radio">
                                        <input type="radio" name="DustomsGoodsType" value="2">
                                    </span>敏感产品
                                </span>&nbsp;&nbsp;&nbsp;
                                <span class="input_radio_box ">
                                    <span class="input_radio">
                                        <input type="radio" name="DustomsGoodsType" value="3">
                                    </span>带有电池
                                </span>&nbsp;&nbsp;&nbsp;
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>英文描述</label>
                            <div class="input">
                                <input type="text" class="box_input" name="DustomsDescriptionEn" value="" size="48"
                                       maxlength="50">
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>中文描述</label>
                            <div class="input">
                                <input type="text" class="box_input" name="DustomsDescriptionCn" value="" size="48"
                                       maxlength="50">
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>Hscode</label>
                            <div class="input">
                                <input type="text" class="box_input" name="DustomsHsCode" value="" size="48">
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>海关申报单价 (不填默认用商城价)</label>
                            <div class="input">
                                <span class="unit_input">
                                    <b>@Model.Currency</b><input type="text" class="box_input left_radius"
                                                   name="CustomsValue" value="" size="20" maxlength="255" rel="amount">
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="global_container">
                    <div class="big_title">
                        SEO <a href="javascript:;" class="btn_edit" id="edit_seo_list"
                               data-save="@(Model.ProductDetail != null && !string.IsNullOrEmpty(Model.ProductDetail.Product.PageUrl) ? "1" : "0")">编辑</a>
                    </div>
                    <div class="seo_info_box">
                        <div class="title clean color_000"
                             style="@(Model.ProductDetail != null && !string.IsNullOrEmpty(Model.ProductDetail.ProductSeo?.SeoTitle_en) ? "" : "display:none;")">
                            @(Model.ProductDetail?.ProductSeo?.SeoTitle_en)
                        </div>
                        <div class="description clean color_555"
                             style="@(Model.ProductDetail != null && !string.IsNullOrEmpty(Model.ProductDetail.ProductSeo?.SeoDescription_en) ? "" : "display:none;")">
                            @(Model.ProductDetail?.ProductSeo?.SeoDescription_en)
                        </div>
                        <div class="keyword clean color_555"
                             style="@(Model.ProductDetail != null && !string.IsNullOrEmpty(Model.ProductDetail.ProductSeo?.SeoKeyword_en) ? "" : "display:none;")">
                            @(Model.ProductDetail?.ProductSeo?.SeoKeyword_en)
                        </div>
                        <div class="url input_copy clean color_888"
                             style="@(Model.ProductDetail != null && !string.IsNullOrEmpty(Model.ProductDetail.Product.PageUrl) ? "" : "display:none;")">
                            @if (Model.ProductDetail != null && !string.IsNullOrEmpty(Model.ProductDetail.Product.PageUrl))
                            {
                                @(Model.ProductUrlBase)@(Model.ProductDetail.Product.PageUrl)
                            }
                        </div>
                        <a href="javascript:;" class="btn_copy" data-clipboard-action="copy"
                           data-clipboard-target=".input_copy">复制链接</a>
                        <div class="blank9"></div>
                    </div>
                    <div class="seo_box global_seo_box">
                        <div class="rows clean multi_lang" data-name="title">
                            <label>
                                标题<span class="tool_tips_ico"
                                          content="即TKD中的Title.每个页面都有自己的Title,作为搜索引擎对页面内容主题判断的第一个标识.建议包含页面相关关键词并提出当前页面内容价值.字符数控制在70以内.">
                                </span>
                            </label>
                            <div class="input">
                                <div class="lang_txt lang_txt_en" style="display:block;" data-default="1" lang="en">
                                    <span class="unit_input">
                                        <div class="number_limit_relative" style="position:relative">
                                            <input data-auto-change="_en" type="text" class="box_input"
                                                   name="SeoTitle_en"
                                                   value="@(Model.ProductDetail?.ProductSeo?.SeoTitle_en)" size="48"
                                                   autocomplete="off" maxlength="255" number_limit=""
                                                   style="width: 336px;">
                                        </div>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="rows clean multi_lang" data-name="description">
                            <label>
                                描述<span class="tool_tips_ico"
                                          content="即TKD中的Description.对页面内容的描述,可以理解为对Title的补充,建议出现三次关键词,内容要精简具有可读性.字符数控制在220以内.">
                                </span>
                            </label>
                            <div class="input">
                                <div class="lang_txt lang_txt_en" style="display:block;" data-default="1" lang="en">
                                    <span class="unit_input unit_textarea" parent_null="">
                                        <div class="number_limit_relative" style="position:relative">
                                            <textarea data-auto-change="_en" name="SeoDescription_en" maxlength="500"
                                                      cols="50" class="box_textarea" number_limit=""
                                                      style="width: 336px;">@(Model.ProductDetail?.ProductSeo?.SeoDescription_en)</textarea>
                                        </div>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="rows clean multi_lang" data-name="keyword">
                            <label>
                                关键词<span class="tool_tips_ico"
                                            content="即TKD中的Keywords.建议放上与页面内容相关的核心关键词和对应的长尾关键词, 建议根据关键词长度设置8-10个关键词.">
                                </span>
                            </label>
                            <dl class="box_basic_more box_seo_basic_more">
                                <dt><a href="javascript:;" class="btn_basic_more"><i></i></a></dt>
                                <dd class="drop_down">
                                    <a href="javascript:;"
                                       class="item input_checkbox_box btn_open_attr"
                                       id="edit_keyword"><span>修改</span></a>
                                </dd>
                            </dl>
                            <div class="rows keys_row clean">
                                <div class="input">
                                    <div class="box_option_list">
                                        <div class="option_selected" data-type="seo_keyword">
                                            <div class="select_list">
                                                @if (Model.ProductDetail != null && !string.IsNullOrEmpty(Model.ProductDetail.ProductSeo?.SeoKeyword_en))
                                                {
                                                    @foreach (var keyword in Model.ProductDetail.ProductSeo?.SeoKeyword_en.Split(','))
                                                    {
                                                        if (!string.IsNullOrWhiteSpace(keyword))
                                                        {
                                                            <span class="btn_attr_choice current"
                                                                  data-type="seo_keyword">
                                                                <b>@keyword.Trim()</b>
                                                                <input type="checkbox" name="seo_keywordCurrent[]"
                                                                       value="ADD:@(keyword.GetHashCode())"
                                                                       class="option_current" checked>
                                                                <input type="hidden" name="seo_keywordOption[]"
                                                                       value="ADD:@(keyword.GetHashCode())">
                                                                <input type="hidden" name="seo_keywordName[]"
                                                                       value="@keyword.Trim()">
                                                                <i></i>
                                                            </span>
                                                        }
                                                    }
                                                }
                                            </div>
                                            <input type="text" class="box_input"
                                                   name="_Option" value="" size="30" maxlength="255">
                                            <span class="placeholder"
                                                  style="@(Model.ProductDetail != null && !string.IsNullOrEmpty(Model.ProductDetail.ProductSeo?.SeoKeyword_en) ? "display:none;" : "")">填写好内容，按回车键或者输入英文逗号保存</span>
                                        </div>
                                        <div class="option_button_menu" style="display:none;">
                                            <a href="javascript:;"
                                               data-type="keys" class="current">标签</a>
                                        </div>
                                        <input type="hidden" class="option_max_number" value="0">
                                        <input type="hidden" name="SeoKeyword_en"
                                               value="@(Model.ProductDetail?.ProductSeo?.SeoKeyword_en)">
                                        <input type="hidden" name="SeoKeyword_en_value"
                                               value="@(Model.ProductDetail?.ProductSeo?.SeoKeyword_en)">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="custom_url_container" class="custom-url-section" style="display: none;">
                        @if (Model.ProductId > 0)
                        {
                            <div class="rows custom_row clean" data-name="url">
                                <label class="fl">自定义地址</label>
                                <a class="btn_copy fr" href="javascript:;" data-clipboard-action="copy"
                                   data-clipboard-target="#pageUrl">复制链接</a>
                                <div class="clear"></div>
                                <div class="input">
                                    <div class="prefix_textarea">
                                        <div class="prefix">@(Model.ProductUrlBase ?? "https://www.retekess.it")<i></i>
                                        </div>
                                        <textarea id="pageUrl" name="PageUrl" class="box_textarea"
                                                  data-domain="@(Model.ProductUrlBase ?? "https://www.retekess.it")"
                                                  style="top: 11px; text-indent: 154px; height: 99.3333px;">@(Model.ProductDetail?.Product.PageUrl ?? "nuove-uscite")</textarea>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
                <div class="global_container" data-name="tag_info">
                    <div class="big_title no_pointer">
                        标签
                        <div class="box_explain">客户可通过搜索标签词语找到相关产品</div>
                    </div>
                    <div class="rows tags_row clean">
                        <div class="input">
                            <div class="box_option_list">
                                <div class="option_selected" data-type="tags">
                                    <div class="select_list">
                                        @if (Model.ProductTags.Any())
                                        {
                                            foreach (var tag in Model.ProductTags)
                                            {
                                                <span class="btn_attr_choice current" data-type="tags"
                                                      data-id="@tag.TId">
                                                    <b>@tag.Name_en</b>
                                                    <input type="hidden" name="tagsOption[]" value="@tag.Name_en">
                                                    <input type="hidden" name="tagsName[]" value="@tag.Name_en">
                                                    <input type="hidden" name="tagsId[]" value="@tag.TId">
                                                    <i></i>
                                                </span>
                                            }
                                        }
                                    </div>
                                    <input type="text" class="box_input" autocomplete="off" name="_Option" value=""
                                           size="30" maxlength="255">
                                    <span class="placeholder"
                                          style="@(Model.ProductTags != null && Model.ProductTags.Any() ? "display:none;" : "")">填写好内容，按回车键或者输入英文逗号保存</span>
                                </div>
                                <div class="option_not_yet" style="display: none;">
                                    <div class="select_list" data-type="tags">
                                        @if (Model.AllTags.Any())
                                        {
                                            foreach (var tag in Model.AllTags.Where(t => t.ProId == 0).OrderBy(t => t.MyOrder))
                                            {
                                                <span class="btn_attr_choice" data-type="tags">
                                                    <b>@tag.Name_en</b>
                                                    <input type="checkbox" name="tagsCurrent[]" value="@tag.TId"
                                                           class="option_current">
                                                    <input type="hidden" name="tagsOption[]" value="@tag.TId">
                                                    <input type="hidden" name="tagsName[]" value="@tag.Name_en">
                                                    <i></i>
                                                </span>
                                            }
                                        }
                                    </div>
                                </div>
                                <div class="option_button" style="display: none;">
                                    <a href="javascript:;" class="select_all">全选</a>
                                    <div class="option_button_menu" style="display:none;">
                                        <a href="javascript:;" data-type="tags" class="current">标签</a>
                                    </div>
                                </div>
                                <input type="hidden" class="option_max_number" value="0">
                                <input type="hidden" name="TagString"
                                       value="@(Model.ProductTags != null && Model.ProductTags.Any() ? string.Join(",", Model.ProductTags.Select(t => t.Name_en)) : "")">
                                <input type="hidden" name="TagIds"
                                       value="@(Model.ProductTags != null && Model.ProductTags.Any() ? $"|{string.Join("|", Model.ProductTags.Select(t => t.TId))}|" : "")">
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <input type="hidden" id="ProId" name="ProId" value="@Model.ProductId">
            <input type="hidden" id="Model" name="Model" value="">
            <input type="hidden" name="do_action" value="/manage/products/products/update?status=0">
            <input type="hidden" id="IsCombination" value="@(Model.ProductDetail?.Product.IsCombination ?? 0)">
            <input type="hidden" id="save_drafts" name="SaveDrafts" value="0">
            <input type="hidden" id="attr_max_number" value="0">
            <input type="hidden" id="option_max_number" value="0">
            <input type="hidden" id="tags_max_number" value="0">
            <input type="hidden" id="VideoUrl" name="VideoUrl" value="@(Model.ProductDetail?.Product.VideoUrl)">
            <input type="hidden" id="VideoType" name="VideoType" value="@(Model.ProductDetail?.Product.VideoType)">
            <input type="hidden" id="VideoTitleSeo" name="VideoTitleSeo" value="">
            <input type="hidden" id="VideoDescriptionSeo" name="VideoDescriptionSeo" value="">
            <input type="hidden" id="VideoPulishTimeSeo" name="VideoPulishTimeSeo" value="1743574533">
            <input type="hidden" name="Warehouse" value="[]">
        </form>
    </div>
    <div id="fixed_right">
        <div class="global_container fixed_video" data-width="410">
            <div class="top_title">上传产品视频 <a href="javascript:;" class="close"></a></div>
            <form class="global_form" id="video_form">
                <div class="rows clean video_source">
                    <label>视频来源</label>
                    <div class="input clean">
                        <div class="box_type_menu">
                            <span class="item">
                                <input type="radio" name="VideoType" value="third">
                                第三方视频
                            </span>
                            <span class="item">
                                <input type="radio" name="VideoType" value="local">
                                本地视频
                            </span>
                        </div>
                    </div>
                </div>
                <div class="rows clean hide" data-videotype="third">
                    <label>粘贴视频地址或嵌入视频代码</label>
                    <div class="input">
                        <textarea name="VideoUrl" class="box_textarea full_textarea"></textarea>
                    </div>
                </div>
                <div class="rows clean " data-videotype="local">
                    <label>本地视频</label>
                    <div class="input">
                        <div class="multi_file upload_file_multi " id="localVideo">
                            <dl class="file  " num="0">
                                <dt class="upload_box preview_file">
                                    <input type="button" class="btn_ok upload_btn"
                                           name="submit_button" value="上传图片" tips=""><input type="hidden"
                                                                                                name="FilePath"
                                                                                                value="" data-value=""
                                                                                                save="0"><input
                                        type="hidden" name="fileCover"
                                        value="/manage/web/shop/images/set/default_cover.jpg">
                                </dt>
                                <dd class="file_btn">
                                    <a href="javascript:;" class="zoom" target="_blank">
                                        <i class="icon_multi_view"></i>
                                    </a><a href="javascript:;" class="del"
                                           rel="del"><i class="icon_multi_delete"></i></a>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
                <div class="rows clean cover_img">
                    <label>封面</label>
                    <div class="tips ">选填，如不上传，则系统自动截取视频第一个画面作为封面</div>
                    <div class="input">
                        <div class="multi_img upload_file_multi " id="Video">
                            <dl class="img " num="0">
                                <dt class="upload_box preview_pic">
                                    <input type="button" class="btn_ok upload_btn"
                                           name="submit_button" value="上传图片" tips=""><input type="hidden"
                                                                                                name="PicPath"
                                                                                                value="" data-value=""
                                                                                                save="0">
                                </dt>
                                <dd class="pic_btn">
                                    <a href="javascript:;" class="zoom" target="_blank">
                                        <i class="icon_multi_view"></i>
                                    </a><a href="javascript:;" class="del"
                                           rel="del"><i class="icon_multi_delete"></i></a>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="submit" class="btn_global btn_submit" value="确定">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
            </form>
            <div class="loading_mask"></div>
        </div>
        <div class="global_container fixed_add_attribute" data-width="600">
            <div class="top_title"><strong>添加属性<span></span></strong> <a href="javascript:;" class="close"></a>
            </div>
            <div class="rows clean" data-type="name">
                <label>名称</label>
                <div class="input">
                    <input type="text" name="AttrName" value="" class="box_input" size="50" maxlength="200"
                           autocomplete="off" notnull="">
                    <p class="error_tips"></p>
                </div>
            </div>
            <div class="rows clean" data-type="type">
                <label>选项类型</label>
                <div class="input">
                    <span class="input_radio_box input_radio_radius_box tab_option fl">
                        <span class="input_radio"><input type="radio" name="Type" value="text" checked=""></span>
                        <em></em>
                        <span class="fs14">文字</span>
                    </span>
                    <span class="input_radio_box input_radio_radius_box tab_option fl">
                        <span class="input_radio"><input type="radio" name="Type" value="color"></span>
                        <em></em>
                        <span class="fs14">颜色</span>
                    </span>
                    <span class="input_radio_box input_radio_radius_box tab_option fl">
                        <span class="input_radio"><input type="radio" name="Type" value="picture"></span>
                        <em></em>
                        <span class="fs14">图文</span>
                    </span>
                </div>
            </div>
            <div class="rows clean box_button box_submit">
                <div class="input">
                    <input type="submit" class="btn_global btn_submit" value="确定">
                    <input type="button" class="btn_global btn_cancel" value="取消">
                </div>
            </div>
        </div>
        <div class="global_container fixed_edit_attribute" data-width="450">
            <div class="top_title"><strong>属性排序</strong><a href="javascript:;" class="close"></a></div>
            <div class="edit_attr_list"></div>
            <div class="rows clean box_button box_submit">
                <div class="input">
                    <input type="submit" class="btn_global btn_submit" value="确定">
                    <input type="button" class="btn_global btn_cancel" value="取消">
                </div>
            </div>
            <div class="bg_no_table_data bg_no_table_fixed">
                <div class="content">
                    <p class="color_000">您当前没有添加选项</p><a href="javascript:;"
                                                                  class="btn_global btn_add_item btn_cancel">关闭</a>
                </div>
            </div>
        </div>
        <div class="global_container fixed_edit_attribute_option_edit" data-width="450">
            <div class="top_title"><strong>修改</strong><a href="javascript:;" class="close"></a></div>
            <div class="attribute_info clean">
                <div class="attribute_title"></div>
            </div>
            <div class="edit_attr_list"></div>
            <div class="rows clean box_button box_submit">
                <div class="input">
                    <input type="submit" class="btn_global btn_submit" value="确定">
                    <input type="button" class="btn_global btn_cancel" value="取消">
                </div>
            </div>
            <div class="bg_no_table_data bg_no_table_fixed">
                <div class="content">
                    <p class="color_000">您当前没有添加选项</p><a href="javascript:;"
                                                                  class="btn_global btn_add_item btn_cancel">关闭</a>
                </div>
            </div>
        </div>
        <div class="global_container fixed_edit_attribute_picture_edit" data-width="450">
            <div class="top_title"><strong>主图</strong><a href="javascript:;" class="close"></a></div>
            <div class="edit_attr_list"></div>
            <div class="rows clean box_button box_submit">
                <div class="input">
                    <input type="submit" class="btn_global btn_submit" value="保存">
                    <input type="button" class="btn_global btn_cancel" value="取消">
                </div>
            </div>
            <div class="bg_no_table_data bg_no_table_fixed">
                <div class="content">
                    <p class="color_000">您当前没有添加选项</p><a href="javascript:;"
                                                                  class="btn_global btn_add_item btn_cancel">关闭</a>
                </div>
            </div>
        </div>
        <div class="global_container fixed_add_combination" data-width="450">
            <div class="top_title"><strong>添加规格</strong><a href="javascript:;" class="close"></a></div>
            <div class="add_content"></div>
            <div class="rows clean box_button box_submit">
                <div class="input">
                    <input type="submit" class="btn_global btn_submit" value="确定">
                    <input type="button" class="btn_global btn_cancel" value="取消">
                </div>
            </div>
            <div class="bg_no_table_data bg_no_table_fixed">
                <div class="content">
                    <p class="color_000">您当前没有添加选项</p><a href="javascript:;"
                                                                  class="btn_global btn_add_item btn_cancel">关闭</a>
                </div>
            </div>
        </div>
        <div class="global_container fixed_batch_edit" data-width="350">
            <div class="top_title"><strong>批量处理</strong><a href="javascript:;" class="close"></a></div>
            <div class="batch_content">
                <div class="batch_model" data-number="0">
                    <div class="batch_item">
                        <div class="title clean">
                            <div class="color_000 fl">价格加价</div>
                            <div class="switchery fl">
                                <input type="checkbox" name="batch_checked">
                                <div class="switchery_toggler"></div>
                                <div class="switchery_inner">
                                    <div class="switchery_state_on"></div>
                                    <div class="switchery_state_off"></div>
                                </div>
                            </div>
                        </div>
                        <div class="input clean">

                            <div class="box_type_menu">
                                <span class="item checked">
                                    <input type="radio" name="options" value="specify" checked="checked">
                                    指定价格
                                </span>
                                <span class="item ">
                                    <input type="radio" name="options" value="adjustment">
                                    调整价格
                                </span>
                            </div>

                            <div class="box_type_content global_form">
                                <div class="item_specify item_content">
                                    <span class="unit_input">
                                        <b>@Model.Currency</b><input type="text" class="box_input" value="" size="16" maxlength="255"
                                                       rel="amount" placeholder="0.00" data-type="price">
                                    </span>
                                </div>
                                <div class="item_adjustment item_content hide">
                                    <div class="rows">
                                        <div class="input content_box">
                                            <div class="box_select">
                                                <select name="adjustment">
                                                    <option value="increase">增加金额</option>
                                                    <option value="reduce">减少金额</option>
                                                    <option value="increase_per">增加百分比</option>
                                                    <option value="per_reduction">减少百分比</option>
                                                </select>
                                            </div>
                                            <div class="box_price">
                                                <span class="unit_input">
                                                    <b class="symbol">@Model.Currency</b><input type="text"
                                                                                  class="box_input" value="" size="16"
                                                                                  maxlength="255"
                                                                                  rel="amount" placeholder="0.00"
                                                                                  data-type="price"><b class="last per"
                                                                                                       style="display: none;">%</b>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="batch_item_overlay"></div>
                        </div>
                    </div>
                    <div class="batch_item">
                        <div class="title clean">
                            <div class="color_000 fl">原价加价</div>
                            <div class="switchery fl">
                                <input type="checkbox" name="batch_checked">
                                <div class="switchery_toggler"></div>
                                <div class="switchery_inner">
                                    <div class="switchery_state_on"></div>
                                    <div class="switchery_state_off"></div>
                                </div>
                            </div>
                        </div>
                        <div class="input clean">

                            <div class="box_type_menu">
                                <span class="item checked">
                                    <input type="radio" name="options" value="specify" checked="checked">
                                    指定价格
                                </span>
                                <span class="item ">
                                    <input type="radio" name="options" value="adjustment">
                                    调整价格
                                </span>
                            </div>

                            <div class="box_type_content global_form">
                                <div class="item_specify item_content">
                                    <span class="unit_input">
                                        <b>@Model.Currency</b><input type="text" class="box_input" value="" size="16" maxlength="255"
                                                       rel="amount" placeholder="0.00" data-type="oldprice">
                                    </span>
                                </div>
                                <div class="item_adjustment item_content hide">
                                    <div class="rows">
                                        <div class="input content_box">
                                            <div class="box_select">
                                                <select name="adjustment">
                                                    <option value="increase">增加金额</option>
                                                    <option value="reduce">减少金额</option>
                                                    <option value="increase_per">增加百分比</option>
                                                    <option value="per_reduction">减少百分比</option>
                                                </select>
                                            </div>
                                            <div class="box_price">
                                                <span class="unit_input">
                                                    <b class="symbol">€</b><input type="text"
                                                                                  class="box_input" value="" size="16"
                                                                                  maxlength="255"
                                                                                  rel="amount" placeholder="0.00"
                                                                                  data-type="oldprice"><b
                                                        class="last per" style="display: none;">%</b>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="batch_item_overlay"></div>
                        </div>
                    </div>
                </div>
                <div class="batch_model" data-number="1">
                    <div class="batch_item">
                        <div class="title clean">
                            <div class="color_000 fl">配图</div>
                            <div class="switchery fl">
                                <input type="checkbox" name="batch_checked">
                                <div class="switchery_toggler"></div>
                                <div class="switchery_inner">
                                    <div class="switchery_state_on"></div>
                                    <div class="switchery_state_off"></div>
                                </div>
                            </div>
                        </div>
                        <div class="input clean">
                            <div class="attr_picture attr_picture_empty"></div>
                            <input type="hidden" value="" data-type="picture">
                            <div class="batch_item_overlay"></div>
                        </div>
                    </div>
                    <div class="batch_item">
                        <div class="title clean">
                            <div class="color_000 fl">SKU</div>
                            <div class="switchery fl">
                                <input type="checkbox" name="batch_checked">
                                <div class="switchery_toggler"></div>
                                <div class="switchery_inner">
                                    <div class="switchery_state_on"></div>
                                    <div class="switchery_state_off"></div>
                                </div>
                            </div>
                        </div>
                        <div class="input clean">
                            <input type="text" class="box_input full_input" value="" size="24" maxlength="255"
                                   autocomplete="off" data-type="sku">
                            <span class="input_checkbox_box">
                                <span class="input_checkbox">
                                    <input type="checkbox">
                                </span>sku里的数字递增1
                            </span>
                            <div class="batch_item_overlay"></div>
                        </div>
                    </div>
                    <div class="batch_item">
                        <div class="title clean">
                            <div class="color_000 fl">原价</div>
                            <div class="switchery fl">
                                <input type="checkbox" name="batch_checked">
                                <div class="switchery_toggler"></div>
                                <div class="switchery_inner">
                                    <div class="switchery_state_on"></div>
                                    <div class="switchery_state_off"></div>
                                </div>
                            </div>
                        </div>
                        <div class="input clean">

                            <div class="box_type_menu">
                                <span class="item checked">
                                    <input type="radio" name="options" value="specify" checked="checked">
                                    指定价格
                                </span>
                                <span class="item ">
                                    <input type="radio" name="options" value="adjustment">
                                    调整价格
                                </span>
                            </div>

                            <div class="box_type_content global_form">
                                <div class="item_specify item_content">
                                    <span class="unit_input">
                                        <b>@Model.Currency</b><input type="text" class="box_input" value="" size="16" maxlength="255"
                                                       rel="amount" placeholder="0.00" data-type="oldprice">
                                    </span>
                                </div>
                                <div class="item_adjustment item_content hide">
                                    <div class="rows">
                                        <div class="input content_box">
                                            <div class="box_select">
                                                <select name="adjustment">
                                                    <option value="increase">增加金额</option>
                                                    <option value="reduce">减少金额</option>
                                                    <option value="increase_per">增加百分比</option>
                                                    <option value="per_reduction">减少百分比</option>
                                                </select>
                                            </div>
                                            <div class="box_price">
                                                <span class="unit_input">
                                                    <b class="symbol">@Model.Currency</b><input type="text"
                                                                                  class="box_input" value="" size="16"
                                                                                  maxlength="255"
                                                                                  rel="amount" placeholder="0.00"
                                                                                  data-type="oldprice"><b
                                                        class="last per" style="display: none;">%</b>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="batch_item_overlay"></div>
                        </div>
                    </div>
                    <div class="batch_item">
                        <div class="title clean">
                            <div class="color_000 fl">价格</div>
                            <div class="switchery fl">
                                <input type="checkbox" name="batch_checked">
                                <div class="switchery_toggler"></div>
                                <div class="switchery_inner">
                                    <div class="switchery_state_on"></div>
                                    <div class="switchery_state_off"></div>
                                </div>
                            </div>
                        </div>
                        <div class="input clean">

                            <div class="box_type_menu">
                                <span class="item checked">
                                    <input type="radio" name="options" value="specify" checked="checked">
                                    指定价格
                                </span>
                                <span class="item ">
                                    <input type="radio" name="options" value="adjustment">
                                    调整价格
                                </span>
                            </div>

                            <div class="box_type_content global_form">
                                <div class="item_specify item_content">
                                    <span class="unit_input">
                                        <b>@Model.Currency</b><input type="text" class="box_input" value="" size="16" maxlength="255"
                                                       rel="amount" placeholder="0.00" data-type="price">
                                    </span>
                                </div>
                                <div class="item_adjustment item_content hide">
                                    <div class="rows">
                                        <div class="input content_box">
                                            <div class="box_select">
                                                <select name="adjustment">
                                                    <option value="increase">增加金额</option>
                                                    <option value="reduce">减少金额</option>
                                                    <option value="increase_per">增加百分比</option>
                                                    <option value="per_reduction">减少百分比</option>
                                                </select>
                                            </div>
                                            <div class="box_price">
                                                <span class="unit_input">
                                                    <b class="symbol">@Model.Currency</b><input type="text"
                                                                                  class="box_input" value="" size="16"
                                                                                  maxlength="255"
                                                                                  rel="amount" placeholder="0.00"
                                                                                  data-type="price"><b class="last per"
                                                                                                       style="display: none;">%</b>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="batch_item_overlay"></div>
                        </div>
                    </div>
                    <div class="batch_item">
                        <div class="title clean">
                            <div class="color_000 fl">成本价</div>
                            <div class="switchery fl">
                                <input type="checkbox" name="batch_checked">
                                <div class="switchery_toggler"></div>
                                <div class="switchery_inner">
                                    <div class="switchery_state_on"></div>
                                    <div class="switchery_state_off"></div>
                                </div>
                            </div>
                        </div>
                        <div class="input clean">

                            <div class="box_type_menu">
                                <span class="item checked">
                                    <input type="radio" name="options" value="specify" checked="checked">
                                    指定价格
                                </span>
                                <span class="item ">
                                    <input type="radio" name="options" value="adjustment">
                                    调整价格
                                </span>
                            </div>

                            <div class="box_type_content global_form">
                                <div class="item_specify item_content">
                                    <span class="unit_input">
                                        <b>@Model.Currency</b><input type="text" class="box_input" value="" size="16" maxlength="255"
                                                       rel="amount" placeholder="0.00" data-type="cost_price">
                                    </span>
                                </div>
                                <div class="item_adjustment item_content hide">
                                    <div class="rows">
                                        <div class="input content_box">
                                            <div class="box_select">
                                                <select name="adjustment">
                                                    <option value="increase">增加金额</option>
                                                    <option value="reduce">减少金额</option>
                                                    <option value="increase_per">增加百分比</option>
                                                    <option value="per_reduction">减少百分比</option>
                                                </select>
                                            </div>
                                            <div class="box_price">
                                                <span class="unit_input">
                                                    <b class="symbol">@Model.Currency</b><input type="text"
                                                                                  class="box_input" value="" size="16"
                                                                                  maxlength="255"
                                                                                  rel="amount" placeholder="0.00"
                                                                                  data-type="cost_price"><b
                                                        class="last per" style="display: none;">%</b>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="batch_item_overlay"></div>
                        </div>
                    </div>
                    <div class="batch_item">
                        <div class="title clean">
                            <div class="color_000 fl">库存</div>
                            <div class="switchery fl">
                                <input type="checkbox" name="batch_checked">
                                <div class="switchery_toggler"></div>
                                <div class="switchery_inner">
                                    <div class="switchery_state_on"></div>
                                    <div class="switchery_state_off"></div>
                                </div>
                            </div>
                        </div>
                        <div class="input clean">
                            <input type="text" class="box_input" value="" size="24" maxlength="255" autocomplete="off"
                                   data-type="stock">
                            <div class="batch_item_overlay"></div>
                        </div>
                    </div>
                    <div class="batch_item">
                        <div class="title clean">
                            <div class="color_000 fl">重量</div>
                            <div class="switchery fl">
                                <input type="checkbox" name="batch_checked">
                                <div class="switchery_toggler"></div>
                                <div class="switchery_inner">
                                    <div class="switchery_state_on"></div>
                                    <div class="switchery_state_off"></div>
                                </div>
                            </div>
                        </div>
                        <div class="input clean">
                            <input type="text" class="box_input" value="" size="24" maxlength="255" autocomplete="off"
                                   data-type="weight">
                            <div class="batch_item_overlay"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="rows clean box_button box_submit">
                <div class="input">
                    <input type="submit" class="btn_global btn_submit" value="全部应用">
                    <input type="button" class="btn_global btn_cancel" value="取消">
                </div>
            </div>
            <div class="bg_no_table_data bg_no_table_fixed">
                <div class="content">
                    <p class="color_000">您当前没有添加选项</p><a href="javascript:;"
                                                                  class="btn_global btn_add_item btn_cancel">关闭</a>
                </div>
            </div>
        </div>
        <div class="global_container fixed_edit_keyword" data-width="350">
            <div class="top_title"><strong>修改关键词</strong><a href="javascript:;" class="close"></a></div>
            <form class="global_form" id="edit_keyword_form">
                <div class="edit_keyword_list"></div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="submit" class="btn_global btn_submit" value="确定"><input type="button"
                                                                                               class="btn_global btn_cancel"
                                                                                               value="取消">
                    </div>
                </div>
                <div class="bg_no_table_data bg_no_table_fixed">
                    <div class="content">
                        <p class="color_000">您当前没有添加选项</p><a href="javascript:;"
                                                                      class="btn_global btn_add_item btn_cancel">关闭</a>
                    </div>
                </div>
                <input type="hidden" name="ProId" value="0"><input type="hidden" name="Model" value=""><input
                    type="hidden" name="do_action" value="/manage/products/products/edit-keyword">
            </form>
        </div>
        <div class="global_container fixed_warehouse_set" data-width="352">
            <div class="top_title"><strong>设置</strong><a href="javascript:;" class="close"></a></div>
            <div class="warehouse_list">
                @if (Model.StockList.Any())
                {
                    foreach (var item in Model.StockList)
                    {
                        <div class="warehouse_item clean" data-id="@item.Value">
                            <div class="btn_checkbox ">
                                <em class="button"></em><input type="checkbox" name="id[]" value="@item.Value">
                            </div>
                            <span class="item_name">@item.Text</span>
                            <input type="hidden" name="saveId[]" value="@item.Value">
                        </div>
                    }
                }
            </div>
            <div class="rows clean box_button box_submit">
                <div class="input">
                    <span class="select_all_box">
                        <span class="input_checkbox_box">
                            <span class="input_checkbox">
                                <input type="checkbox" name="select_all_item"
                                       value="1">
                            </span>全选
                        </span>
                    </span>
                    <input type="button" class="btn_global btn_submit" name="submit_button" value="确定"> <input
                        type="button" class="btn_global btn_cancel" value="返回">
                </div>
            </div>
        </div>
        <div class="global_container fixed_edit_alt" data-width="380">
            <div class="top_title"><strong>添加Alt属性</strong><a href="javascript:;" class="close"></a></div>
            <form id="alt_edit_form" class="global_form">
                <div class="input">
                    <textarea name="Alt" notnull="notnull" class="box_textarea"
                              style="height:120px;"></textarea>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="hidden" name="Num" value=""> <input type="hidden" name="boxId"
                                                                         value=""> <input type="submit"
                                                                                          class="btn_global btn_submit"
                                                                                          value="确定"> <input
                            type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
            </form>
        </div>
        <div class="global_container fixed_supplier_edit" data-width="400"></div>

        <div class="global_container fixed_edit_condition_screening" data-width="450">
            <div class="top_title"><strong>修改扩展条件</strong><a href="javascript:;" class="close"></a></div>
            <form id="edit_condition_screening_form" class="global_form">
                <div class="rows clean condition_screening_box" data-condition_screening="">
                    <div class="input condition_screening_list">
                        <div class="screening_box_attribute">
                            <div class="box_cart_attribute_screening"></div>
                            <div class="rows clean attr_add">
                                <div class="input">
                                    <a href="javascript:;" class="fl" id="add_attribute_screening"
                                       data-cart="1">添加筛选条件</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="button" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="text" class="hide">
                <input type="hidden" name="attrLength" value="">
                <input type="hidden" name="ProId" value="">
                <input type="hidden" name="temSave" value="0">
                <input type="hidden" name="do_action" value="/manage/plugins/screening/data-post">
            </form>
        </div>
        <div class="global_container fixed_video_seo" data-width="410">
            <div class="top_title">视频SEO <a href="javascript:;" class="close"></a></div>
            <form id="video_seo_form" class="global_form">
                <div class="rows clean">
                    <label>视频标题</label>
                    <div class="input">
                        <input type="text" class="box_input full_input" name="VideoTitleSeo" value="" maxlength="255"
                               notnull="">
                    </div>
                </div>
                <div class="rows clean">
                    <label>视频描述</label>
                    <div class="input">
                        <textarea class="box_textarea full_textarea" name="VideoDescriptionSeo" maxlength="255"
                                  notnull=""></textarea>
                    </div>
                </div>
                <div class="rows clean">
                    <label>视频发布时间</label>
                    <div class="input_box">
                        <input name="VideoPulishTimeSeo" type="text" value="2025-04-02 08:15:33"
                               class="start_time box_input full_input input_time" size="45" readonly="">
                    </div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="submit" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                        <input type="hidden" name="do_action" value="/manage/products/products/video-seo-action">
                    </div>
                </div>
            </form>
        </div>

        <div class="global_container fixed_translation" data-width="400">
            <div class="top_title"><strong>翻译</strong><a href="javascript:;" class="close"></a></div>
            <div class="content_box" data-module="products" data-related-id="0" data-shop-language="en" data-len="1"
                 data-track-id="0"></div>
        </div>
        <div class="btn_translation"></div>
    </div>
    <div id="copy_set_box">
        <div class="copy_mask"></div>
        <div class="copy_box">
            <div class="title">复制产品<i class="close iconfont icon-close1"></i></div>
            <form id="copy_box_form" class="global_form">
                <div class="box">
                    <div class="subtitle">选择需要复制的应用信息</div>
                    <div class="list">
                        <span class="input_checkbox_box" data-type="wholesale">
                            <span class="input_checkbox">
                                <input type="checkbox" name="copyApp[]" value="wholesale">
                            </span>
                            批发 <em></em>
                        </span>
                        <span class="input_checkbox_box" data-type="shipping_template">
                            <span class="input_checkbox">
                                <input type="checkbox" name="copyApp[]" value="shipping_template">
                            </span>
                            运费模板 <em></em>
                        </span>
                        <span class="input_checkbox_box" data-type="custom_attributes">
                            <span class="input_checkbox">
                                <input type="checkbox" name="copyApp[]" value="custom_attributes">
                            </span>
                            定制属性 <em></em>
                        </span>
                        <span class="input_checkbox_box" data-type="screening">
                            <span class="input_checkbox">
                                <input type="checkbox" name="copyApp[]" value="screening">
                            </span>
                            条件筛选 <em></em>
                        </span>
                    </div>
                </div>
                <div class="btn_row rows clean">
                    <div class="input">
                        <input type="hidden" name="copyId" value="0">
                        <input type="submit" class="btn_global btn_submit" value="复制产品">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="pop_form global_select_category_popup_box">
        <div class="t">
            <h1>选择分类</h1>
            <h2>×</h2>
        </div>
        <div class="search_form">
            <div class="k_input">
                <input type="text" id="categorySearchInput" name="Keyword" value="@Model.CateName" class="form_input"
                       size="15"
                       autocomplete="off"
                       placeholder="请输入分类名称">
            </div>
            <input type="button" value="搜索" class="search_btn">
            <div class="category_num">
                已选择 <span class="num">0</span> 个分类
            </div>
            <div class="global_app_tips obvious"><em></em><span>仅支持"manual"方式的分类</span></div>
        </div>


        <div class="category_content">
            <div class="category_table select_category_table">
                <div class="thead">
                    <div class="tr">
                        <div class="td">
                            多选<i class="tool_tips_ico" content="批量多选指定分类及其子分类"> </i>
                            &nbsp;&nbsp;分类名称
                        </div>
                    </div>
                </div>
                <div class="tbody">
                    @if (Model.CategoryTree != null && Model.CategoryTree.ContainsKey(0))
                    {
                        @foreach (var topCategory in Model.CategoryTree[0])
                        {
                            <div class="first_box">
                                <div class="tr haschild">
                                    <div class="td btn_select"><span class="icon iconfont icon_menu_listselect"></span>
                                    </div>
                                    <div class="td c_name">
                                        <span
                                            class="input_checkbox_box @(topCategory.AddMethod != "manual" ? "disabled" : "")"
                                            onclick="if(!$(this).hasClass('disabled')){$(this).toggleClass('checked');$(this).find('input').prop('checked', !$(this).find('input').prop('checked'));}">
                                            <span class="input_checkbox">
                                                <input type="checkbox"
                                                       @(topCategory.AddMethod != "manual" ? "disabled" : "")
                                                       name="GlobalSelectCateId"
                                                       data-alias="@topCategory.Category_en"
                                                       value="@topCategory.CateId">
                                            </span>@topCategory.Category_en
                                        </span>
                                    </div>
                                    <div class="td btn_sub"><span class="icon iconfont icon_menu_downarrow"></span>
                                    </div>
                                </div>

                                @if (Model.CategoryTree.ContainsKey(topCategory.CateId) && Model.CategoryTree[topCategory.CateId].Any())
                                {
                                    <div class="second_box box_sub" data-child="@topCategory.CateId">
                                        @foreach (var childCategory in Model.CategoryTree[topCategory.CateId])
                                        {
                                            bool parentDisabled = topCategory.AddMethod != "manual";
                                            bool currentDisabled = parentDisabled || childCategory.AddMethod != "manual";

                                            <div class="tr">
                                                <div class="td btn_select"><span
                                                        class="icon iconfont icon_menu_listselect"></span></div>
                                                <div class="td c_name">
                                                    <span
                                                        class="input_checkbox_box @(currentDisabled ? "disabled" : "")"
                                                        onclick="if(!$(this).hasClass('disabled')){$(this).toggleClass('checked');$(this).find('input').prop('checked', !$(this).find('input').prop('checked'));}">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" @(currentDisabled ? "disabled" : "")
                                                                   name="GlobalSelectCateId"
                                                                   data-alias="@topCategory.Category_en > @childCategory.Category_en"
                                                                   value="@childCategory.CateId">
                                                        </span>@childCategory.Category_en
                                                    </span>
                                                </div>
                                                <div class="td btn_sub">
                                                    @if (Model.CategoryTree.ContainsKey(childCategory.CateId) && Model.CategoryTree[childCategory.CateId].Any())
                                                    {
                                                        <span class="icon iconfont icon_menu_downarrow"></span>
                                                    }
                                                </div>
                                            </div>

                                            @if (Model.CategoryTree.ContainsKey(childCategory.CateId) && Model.CategoryTree[childCategory.CateId].Any())
                                            {
                                                <div class="third_box box_sub" data-child="@childCategory.CateId"
                                                     style="display: none;">
                                                    @foreach (var thirdCategory in Model.CategoryTree[childCategory.CateId])
                                                    {
                                                        bool thirdDisabled = currentDisabled || thirdCategory.AddMethod != "manual";

                                                        <div class="tr">
                                                            <div class="td btn_select"><span
                                                                    class="icon iconfont icon_menu_listselect"></span>
                                                            </div>
                                                            <div class="td c_name">
                                                                <span
                                                                    class="input_checkbox_box @(thirdDisabled ? "disabled" : "")"
                                                                    onclick="if(!$(this).hasClass('disabled')){$(this).toggleClass('checked');$(this).find('input').prop('checked', !$(this).find('input').prop('checked'));}">
                                                                    <span class="input_checkbox">
                                                                        <input type="checkbox"
                                                                               @(thirdDisabled ? "disabled" : "")
                                                                               name="GlobalSelectCateId"
                                                                               data-alias="@topCategory.Category_en > @childCategory.Category_en > @thirdCategory.Category_en"
                                                                               value="@thirdCategory.CateId">
                                                                    </span>@thirdCategory.Category_en
                                                                </span>
                                                            </div>
                                                            <div class="td btn_sub"></div>
                                                        </div>
                                                    }
                                                </div>
                                            }
                                        }
                                    </div>
                                }
                            </div>
                        }
                    }
                </div>
            </div>

            <div class="category_table search_category_table" style="display: none;">
                <div class="thead">
                    <div class="tr">
                        <div class="td">分类名称</div>
                    </div>
                </div>
                <div class="tbody"></div>
                <div class="bg_no_table_data" style="display: none;">
                    <div class="content">
                        <p>当前暂时没有数据</p>
                    </div>
                    <span></span>
                </div>
            </div>

            <div class="button">
                <input type="button" class="btn_global btn_submit" value="保存">
                <input type="button" class="btn_global btn_cancel" value="取消">
            </div>
        </div>
    </div>
    <div class="rows fixed_btn_submit" style="width: 1860px; left: 180px;">
        <div class="center_container_1200">
            <div class="input">
                <input type="submit" class="btn_global btn_submit" name="submit_button" value="保存"
                       onclick="toSaveProduct();return false">
                <input type="button" class="btn_global btn_drafts" value="存草稿" style="display:none;">
                <a href="/Products/Index?status=0" class="btn_global btn_cancel">返回</a>
            </div>
        </div>
    </div>

</div>



